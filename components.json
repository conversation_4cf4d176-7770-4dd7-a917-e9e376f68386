{"$schema": "https://shadcn-vue.com/schema.json", "style": "new-york", "typescript": true, "tailwind": {"config": "", "css": "src/assets/main.css", "baseColor": "zinc", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@registry/components", "composables": "@registry/composables", "utils": "@registry/lib/utils", "ui": "@registry/components/ui", "lib": "@registry/lib"}, "iconLibrary": "lucide"}
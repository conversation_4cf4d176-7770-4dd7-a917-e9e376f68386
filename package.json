{"private": true, "type": "module", "name": "blocks", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.4.1", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "build:repo": "jsrepo build --allow-subdirectories"}, "dependencies": {"@rcode-link/tiptap-comments": "^1.0.2", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-query": "^5.79.2", "@tiptap/core": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-list": "^3.0.3", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.3", "@tiptap/extensions": "^3.0.7", "@tiptap/pm": "^3.0.3", "@tiptap/starter-kit": "^3.0.3", "@tiptap/vue-3": "^3.0.3", "@vueuse/core": "^13.6.0", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "less": "^4.3.0", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "reka-ui": "^2.4.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tippy.js": "^6.3.7", "tiptap-extension-resize-image": "^1.2.2", "tw-animate-css": "^1.3.6", "vue": "^3.5.17", "vue-router": "^4.5.1", "y-websocket": "^3.0.0", "yjs": "^13.6.27"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.3", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsrepo": "^2.2.1", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "typescript": "~5.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}
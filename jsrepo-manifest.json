{"name": "@github/vius/blocks", "categories": [{"name": "notion-like-editor", "blocks": [{"name": "components", "directory": "registry/notion-like-editor/components", "category": "notion-like-editor", "tests": false, "subdirectory": true, "list": true, "files": ["BubbleMenu.vue", "CollaborationStatus.vue", "DragHandle.vue", "FloatingMenu.vue", "TableMenu.vue"], "localDependencies": [], "dependencies": [], "devDependencies": [], "_imports_": {}}, {"name": "extensions", "directory": "registry/notion-like-editor/extensions", "category": "notion-like-editor", "tests": false, "subdirectory": true, "list": true, "files": ["block-extension.js", "drag-drop.js", "markdown-shortcuts.js", "slash-command/CommandsList.vue", "slash-command/commands.js", "slash-command/suggestion.js"], "localDependencies": [], "dependencies": ["@tiptap/core", "@tiptap/pm@^2.25.0", "@tiptap/suggestion@^2.25.0", "@tiptap/vue-3@^2.25.0", "tippy.js@^6.3.7"], "devDependencies": [], "_imports_": {}}, {"name": "index", "directory": "registry/notion-like-editor", "category": "notion-like-editor", "tests": false, "subdirectory": false, "list": true, "files": ["index.vue"], "localDependencies": ["notion-like-editor/extensions", "notion-like-editor/components", "notion-like-editor/services"], "_imports_": {"./extensions/slash-command/commands.js": "{{notion-like-editor/extensions}}/slash-command/commands.js", "./extensions/slash-command/suggestion.js": "{{notion-like-editor/extensions}}/slash-command/suggestion.js", "./extensions/block-extension.js": "{{notion-like-editor/extensions}}/block-extension.js", "./extensions/markdown-shortcuts.js": "{{notion-like-editor/extensions}}/markdown-shortcuts.js", "./extensions/drag-drop.js": "{{notion-like-editor/extensions}}/drag-drop.js", "./components/FloatingMenu.vue": "{{notion-like-editor/components}}/FloatingMenu.vue", "./components/BubbleMenu.vue": "{{notion-like-editor/components}}/BubbleMenu.vue", "./components/CollaborationStatus.vue": "{{notion-like-editor/components}}/CollaborationStatus.vue", "./services/collaboration.js": "{{notion-like-editor/services}}/collaboration.js"}, "dependencies": ["@tiptap/vue-3@^2.25.0", "@tiptap/starter-kit@^2.25.0", "@tiptap/extension-placeholder@^2.25.0", "@tiptap/extension-typography@^2.25.0", "@tiptap/extension-image@^2.25.0", "@tiptap/extension-link@^2.25.0", "@tiptap/extension-table@^2.25.0", "@tiptap/extension-table-row@^2.25.0", "@tiptap/extension-table-header@^2.25.0", "@tiptap/extension-table-cell@^2.25.0", "@tiptap/extension-collaboration@^2.25.0", "@tiptap/extension-collaboration-cursor@^2.25.0"], "devDependencies": []}, {"name": "services", "directory": "registry/notion-like-editor/services", "category": "notion-like-editor", "tests": false, "subdirectory": true, "list": true, "files": ["collaboration.js"], "localDependencies": [], "dependencies": ["yjs@^13.6.27", "y-websocket@^3.0.0"], "devDependencies": [], "_imports_": {}}]}, {"name": "utils", "blocks": [{"name": "request", "directory": "registry/utils", "category": "utils", "tests": false, "subdirectory": false, "list": true, "files": ["request.ts"], "localDependencies": [], "_imports_": {}, "dependencies": ["@tanstack/vue-query@^5.79.2"], "devDependencies": []}]}]}